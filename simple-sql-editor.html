<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر SQL البسيط</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>
    <style>
        .sql-editor {
            width: 100%;
            height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            resize: vertical;
            direction: ltr;
            text-align: left;
        }
        
        .sql-editor:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .results-container {
            max-height: 400px;
            overflow: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .schema-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            border: 1px solid #e5e7eb;
            padding: 8px 12px;
            text-align: right;
        }
        
        th {
            background-color: #f9fafb;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f9fafb;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        
        .sample-btn {
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            margin: 4px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
            display: inline-block;
        }
        
        .sample-btn:hover {
            background-color: #e5e7eb;
            border-color: #9ca3af;
        }
        
        @media (max-width: 768px) {
            .sql-editor {
                height: 150px;
                font-size: 12px;
            }
            
            .sample-btn {
                font-size: 10px;
                padding: 6px 8px;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">محرر SQL البسيط</h1>
            <p class="text-gray-600">اكتب وتنفيذ أوامر SQL مباشرة في المتصفح</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Schema Panel -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">الجداول</h2>
                    <div id="schema-panel" class="schema-container">
                        <p class="text-gray-500 text-sm text-center">لا توجد جداول</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- SQL Editor -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2">
                        <h2 class="text-xl font-semibold text-gray-800">محرر SQL</h2>
                        <div class="flex gap-2">
                            <button id="execute-btn" class="btn btn-primary">تشغيل الأمر</button>
                            <button id="clear-btn" class="btn btn-secondary">مسح</button>
                        </div>
                    </div>
                    <textarea id="sql-editor" class="sql-editor" placeholder="اكتب أوامر SQL هنا...

مثال:
SELECT 'مرحباً بك في محرر SQL!' as message;"></textarea>
                </div>

                <!-- Sample Queries -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">أمثلة سريعة</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                        <button class="sample-btn" data-query="CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    age INTEGER CHECK(age >= 0)
);">إنشاء جدول المستخدمين</button>

                        <button class="sample-btn" data-query="INSERT INTO users (name, email, age) VALUES 
('أحمد محمد', '<EMAIL>', 25),
('فاطمة علي', '<EMAIL>', 30),
('محمد حسن', '<EMAIL>', 28);">إدراج بيانات تجريبية</button>

                        <button class="sample-btn" data-query="SELECT * FROM users ORDER BY age DESC;">عرض جميع المستخدمين</button>

                        <button class="sample-btn" data-query="CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    price DECIMAL(10,2) CHECK(price > 0),
    category TEXT DEFAULT 'عام'
);">إنشاء جدول المنتجات</button>

                        <button class="sample-btn" data-query="INSERT INTO products (name, price, category) VALUES 
('لابتوب Dell', 2500.00, 'إلكترونيات'),
('هاتف iPhone', 3000.00, 'إلكترونيات'),
('كتاب البرمجة', 50.00, 'كتب');">إدراج منتجات</button>

                        <button class="sample-btn" data-query="SELECT 
    category as الفئة,
    COUNT(*) as عدد_المنتجات,
    AVG(price) as متوسط_السعر
FROM products 
GROUP BY category;">إحصائيات المنتجات</button>
                    </div>
                </div>

                <!-- Results Panel -->
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">النتائج</h2>
                    <div id="results-panel" class="results-container">
                        <p class="text-gray-500 text-sm p-4">قم بتشغيل أمر SQL لعرض النتائج هنا</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="text-gray-700">جاري تحميل محرك SQL...</span>
            </div>
        </div>
    </div>

    <script>
        class SimpleSQLEditor {
            constructor() {
                this.db = null;
                this.isInitialized = false;
                this.init();
            }

            async init() {
                try {
                    this.showLoading(true);
                    await this.initSQLJS();
                    this.setupEventListeners();
                    this.isInitialized = true;
                    this.showLoading(false);
                    this.showMessage('تم تحميل محرر SQL بنجاح! جرب الأمثلة السريعة أو اكتب أوامر SQL الخاصة بك.', 'success');
                } catch (error) {
                    this.showLoading(false);
                    this.showMessage('خطأ في تحميل المحرر: ' + error.message, 'error');
                }
            }

            async initSQLJS() {
                const sqlPromise = initSqlJs({
                    locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
                });
                const SQL = await sqlPromise;
                this.db = new SQL.Database();
                console.log('تم تهيئة محرك SQL بنجاح');
            }

            setupEventListeners() {
                // زر تنفيذ الأمر
                document.getElementById('execute-btn').addEventListener('click', () => {
                    this.executeSQL();
                });

                // زر المسح
                document.getElementById('clear-btn').addEventListener('click', () => {
                    document.getElementById('sql-editor').value = '';
                    document.getElementById('sql-editor').focus();
                });

                // الأمثلة السريعة
                document.querySelectorAll('.sample-btn').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const query = e.target.getAttribute('data-query');
                        document.getElementById('sql-editor').value = query;
                        document.getElementById('sql-editor').focus();
                    });
                });

                // اختصار لوحة المفاتيح
                document.getElementById('sql-editor').addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'Enter') {
                        e.preventDefault();
                        this.executeSQL();
                    }
                });
            }

            executeSQL() {
                if (!this.isInitialized) {
                    this.showMessage('المحرر لم يتم تحميله بعد، يرجى الانتظار...', 'warning');
                    return;
                }

                const sqlQuery = document.getElementById('sql-editor').value.trim();
                if (!sqlQuery) {
                    this.showMessage('يرجى كتابة أمر SQL أولاً', 'warning');
                    return;
                }

                try {
                    const startTime = performance.now();
                    const results = this.db.exec(sqlQuery);
                    const endTime = performance.now();
                    const executionTime = (endTime - startTime).toFixed(2);

                    this.displayResults(results, executionTime);
                    this.updateSchemaPanel();
                    
                } catch (error) {
                    this.showMessage('خطأ في تنفيذ الأمر: ' + error.message, 'error');
                }
            }

            displayResults(results, executionTime) {
                const resultsPanel = document.getElementById('results-panel');
                
                if (results.length === 0) {
                    resultsPanel.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <span class="text-green-600 text-lg ml-2">✅</span>
                                <span class="text-green-800 font-medium">تم تنفيذ الأمر بنجاح</span>
                            </div>
                            <p class="text-green-700 text-sm mt-1">وقت التنفيذ: ${executionTime} مللي ثانية</p>
                        </div>
                    `;
                } else {
                    let html = `
                        <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <span class="text-sm font-medium text-blue-800">📊 نتائج الاستعلام - وقت التنفيذ: ${executionTime} مللي ثانية</span>
                        </div>
                    `;
                    
                    results.forEach((result, index) => {
                        if (result.columns && result.values) {
                            html += this.createTable(result.columns, result.values, index);
                        }
                    });
                    
                    resultsPanel.innerHTML = html;
                }
            }

            createTable(columns, values, index) {
                if (values.length === 0) {
                    return `
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                            <p class="text-yellow-800">⚠️ لا توجد نتائج للاستعلام ${index + 1}</p>
                        </div>
                    `;
                }

                let tableHTML = `
                    <div class="mb-6">
                        <div class="bg-gray-50 px-4 py-2 border-b">
                            <h3 class="font-medium text-gray-800">📋 نتائج الاستعلام ${index + 1} (${values.length} صف)</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table>
                                <thead>
                                    <tr>
                `;
                
                columns.forEach(column => {
                    tableHTML += `<th>${this.escapeHtml(column)}</th>`;
                });
                
                tableHTML += `
                                    </tr>
                                </thead>
                                <tbody>
                `;
                
                values.forEach((row) => {
                    tableHTML += `<tr>`;
                    row.forEach(cell => {
                        const cellValue = cell === null ? '<span style="color: #9ca3af; font-style: italic;">NULL</span>' : this.escapeHtml(String(cell));
                        tableHTML += `<td>${cellValue}</td>`;
                    });
                    tableHTML += '</tr>';
                });
                
                tableHTML += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
                
                return tableHTML;
            }

            updateSchemaPanel() {
                try {
                    const tablesResult = this.db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
                    const schemaPanel = document.getElementById('schema-panel');
                    
                    if (tablesResult.length === 0 || tablesResult[0].values.length === 0) {
                        schemaPanel.innerHTML = '<p class="text-gray-500 text-sm text-center">لا توجد جداول</p>';
                        return;
                    }
                    
                    let html = '';
                    const tables = tablesResult[0].values.map(row => row[0]);
                    
                    tables.forEach(tableName => {
                        html += this.createTableSchema(tableName);
                    });
                    
                    schemaPanel.innerHTML = html;
                    
                } catch (error) {
                    console.error('خطأ في تحديث عرض الجداول:', error);
                }
            }

            createTableSchema(tableName) {
                try {
                    const columnsResult = this.db.exec(`PRAGMA table_info(${tableName})`);
                    
                    let html = `
                        <div class="mb-3 p-3 border border-gray-200 rounded-lg">
                            <h4 class="font-medium text-blue-800 mb-2">📋 ${tableName}</h4>
                    `;
                    
                    if (columnsResult.length > 0 && columnsResult[0].values.length > 0) {
                        columnsResult[0].values.forEach(column => {
                            const [cid, name, type, notnull, defaultValue, pk] = column;
                            const constraints = [];
                            if (pk) constraints.push('🔑');
                            if (notnull) constraints.push('⚠️');
                            
                            html += `
                                <div class="text-xs mb-1 p-1 bg-gray-50 rounded">
                                    <span class="font-mono text-blue-600">${name}</span>
                                    <span class="text-gray-600">${type}</span>
                                    ${constraints.length > 0 ? `<span>${constraints.join(' ')}</span>` : ''}
                                </div>
                            `;
                        });
                    }
                    
                    html += `</div>`;
                    return html;
                    
                } catch (error) {
                    return `
                        <div class="mb-3 p-3 border border-red-200 rounded-lg">
                            <h4 class="font-medium text-red-800">❌ ${tableName}</h4>
                            <p class="text-xs text-red-600">خطأ في قراءة الهيكل</p>
                        </div>
                    `;
                }
            }

            showMessage(message, type = 'info') {
                const resultsPanel = document.getElementById('results-panel');
                
                const colors = {
                    success: { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800', icon: '✅' },
                    error: { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-800', icon: '❌' },
                    warning: { bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-800', icon: '⚠️' },
                    info: { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800', icon: 'ℹ️' }
                };
                
                const config = colors[type] || colors.info;
                
                resultsPanel.innerHTML = `
                    <div class="${config.bg} ${config.border} rounded-lg p-4">
                        <div class="flex items-start">
                            <span class="text-lg ml-2">${config.icon}</span>
                            <p class="${config.text}">${message}</p>
                        </div>
                    </div>
                `;
            }

            showLoading(show) {
                const overlay = document.getElementById('loading-overlay');
                if (show) {
                    overlay.classList.remove('hidden');
                } else {
                    overlay.classList.add('hidden');
                }
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleSQLEditor();
        });
    </script>
</body>
</html>
