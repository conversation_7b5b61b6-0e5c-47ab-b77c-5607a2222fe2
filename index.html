<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر ومترجم SQL</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>
    <script src="https://unpkg.com/monaco-editor@0.44.0/min/vs/loader.js"></script>
    <link rel="stylesheet" href="styles.css">
    <style>
        .monaco-editor-container {
            height: 300px;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
        }
        
        .results-table {
            max-height: 400px;
            overflow: auto;
        }
        
        .schema-panel {
            max-height: 500px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div class="container mx-auto p-6">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">محرر ومترجم SQL</h1>
            <p class="text-gray-600">اكتب وتنفيذ أوامر SQL مباشرة في المتصفح</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Schema Panel -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">هيكل قاعدة البيانات</h2>
                    <div id="schema-panel" class="schema-panel">
                        <p class="text-gray-500 text-sm">لا توجد جداول بعد</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- SQL Editor -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">محرر SQL</h2>
                        <div class="space-x-2">
                            <button id="execute-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                                تشغيل الأمر
                            </button>
                            <button id="clear-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors">
                                مسح
                            </button>
                        </div>
                    </div>
                    <div id="sql-editor" class="monaco-editor-container"></div>
                </div>

                <!-- Sample Queries -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">أمثلة سريعة</h3>

                    <!-- أمثلة DDL -->
                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-700 mb-2">إنشاء الجداول (DDL)</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-2 rounded text-sm text-left transition-colors border border-blue-200"
                                    data-query="CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    age INTEGER CHECK(age >= 0 AND age <= 120),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);">
                                إنشاء جدول المستخدمين
                            </button>
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-2 rounded text-sm text-left transition-colors border border-blue-200"
                                    data-query="CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    price DECIMAL(10,2) CHECK(price > 0),
    category TEXT DEFAULT 'عام',
    in_stock BOOLEAN DEFAULT 1
);">
                                إنشاء جدول المنتجات
                            </button>
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-2 rounded text-sm text-left transition-colors border border-blue-200"
                                    data-query="CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER CHECK(quantity > 0),
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);">
                                إنشاء جدول الطلبات
                            </button>
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-2 rounded text-sm text-left transition-colors border border-blue-200"
                                    data-query="CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_products_category ON products(category);">
                                إنشاء فهارس
                            </button>
                        </div>
                    </div>

                    <!-- أمثلة DML -->
                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-700 mb-2">إدراج البيانات (DML)</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-2 rounded text-sm text-left transition-colors border border-green-200"
                                    data-query="INSERT INTO users (name, email, age) VALUES
('أحمد محمد', '<EMAIL>', 25),
('فاطمة علي', '<EMAIL>', 30),
('محمد حسن', '<EMAIL>', 28),
('سارة أحمد', '<EMAIL>', 22),
('علي محمود', '<EMAIL>', 35);">
                                إدراج مستخدمين
                            </button>
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-2 rounded text-sm text-left transition-colors border border-green-200"
                                    data-query="INSERT INTO products (name, price, category) VALUES
('لابتوب Dell', 2500.00, 'إلكترونيات'),
('هاتف iPhone', 3000.00, 'إلكترونيات'),
('كتاب البرمجة', 50.00, 'كتب'),
('قميص قطني', 80.00, 'ملابس'),
('حقيبة جلدية', 150.00, 'إكسسوارات');">
                                إدراج منتجات
                            </button>
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-2 rounded text-sm text-left transition-colors border border-green-200"
                                    data-query="INSERT INTO orders (user_id, product_id, quantity) VALUES
(1, 1, 1),
(2, 2, 1),
(3, 3, 2),
(1, 4, 3),
(4, 5, 1);">
                                إدراج طلبات
                            </button>
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-2 rounded text-sm text-left transition-colors border border-green-200"
                                    data-query="UPDATE products
SET price = price * 0.9
WHERE category = 'ملابس';

UPDATE users
SET age = age + 1
WHERE id IN (1, 2, 3);">
                                تحديث البيانات
                            </button>
                        </div>
                    </div>

                    <!-- أمثلة الاستعلامات -->
                    <div>
                        <h4 class="text-md font-medium text-gray-700 mb-2">الاستعلامات المتقدمة</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-2 rounded text-sm text-left transition-colors border border-purple-200"
                                    data-query="SELECT * FROM users ORDER BY age DESC;">
                                عرض جميع المستخدمين
                            </button>
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-2 rounded text-sm text-left transition-colors border border-purple-200"
                                    data-query="SELECT
    u.name as اسم_المستخدم,
    p.name as اسم_المنتج,
    o.quantity as الكمية,
    (p.price * o.quantity) as المجموع
FROM orders o
JOIN users u ON o.user_id = u.id
JOIN products p ON o.product_id = p.id
ORDER BY المجموع DESC;">
                                تقرير الطلبات
                            </button>
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-2 rounded text-sm text-left transition-colors border border-purple-200"
                                    data-query="SELECT
    category as الفئة,
    COUNT(*) as عدد_المنتجات,
    AVG(price) as متوسط_السعر,
    MIN(price) as أقل_سعر,
    MAX(price) as أعلى_سعر
FROM products
GROUP BY category
ORDER BY عدد_المنتجات DESC;">
                                إحصائيات المنتجات
                            </button>
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-2 rounded text-sm text-left transition-colors border border-purple-200"
                                    data-query="SELECT
    u.name,
    u.age,
    COUNT(o.id) as عدد_الطلبات,
    COALESCE(SUM(p.price * o.quantity), 0) as إجمالي_المشتريات
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
LEFT JOIN products p ON o.product_id = p.id
GROUP BY u.id, u.name, u.age
ORDER BY إجمالي_المشتريات DESC;">
                                تحليل العملاء
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Results Panel -->
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">النتائج</h2>
                    <div id="results-panel" class="results-table">
                        <p class="text-gray-500 text-sm">قم بتشغيل أمر SQL لعرض النتائج هنا</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="text-gray-700">جاري تحميل محرك SQL...</span>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
