<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر SQL البسيط</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>
    <style>
        .sql-editor {
            width: 100%;
            height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            resize: vertical;
            direction: ltr;
            text-align: left;
        }

        .sql-editor:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .results-container {
            max-height: 400px;
            overflow: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .schema-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid #e5e7eb;
            padding: 8px 12px;
            text-align: right;
        }

        th {
            background-color: #f9fafb;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background-color: #f9fafb;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
        }

        .sample-btn {
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            margin: 4px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .sample-btn:hover {
            background-color: #e5e7eb;
            border-color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">محرر SQL البسيط</h1>
            <p class="text-gray-600">اكتب وتنفيذ أوامر SQL مباشرة في المتصفح</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Schema Panel -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">الجداول</h2>
                    <div id="schema-panel" class="schema-container">
                        <p class="text-gray-500 text-sm text-center">لا توجد جداول</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- SQL Editor -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">محرر SQL</h2>
                        <div class="space-x-2">
                            <button id="execute-btn" class="btn btn-primary">تشغيل الأمر</button>
                            <button id="clear-btn" class="btn btn-secondary">مسح</button>
                        </div>
                    </div>
                    <textarea id="sql-editor" class="sql-editor" placeholder="اكتب أوامر SQL هنا...
مثال:
SELECT 'مرحباً بك في محرر SQL!' as message;"></textarea>
                </div>

                <!-- Sample Queries -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-4 py-3 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <span class="ml-2">🚀</span>
                            أمثلة سريعة
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">انقر على أي مثال لتحميله في المحرر</p>
                    </div>

                    <div class="p-4">
                        <!-- أمثلة DDL -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-700 mb-3 flex items-center">
                                <span class="ml-2">🏗️</span>
                                إنشاء الجداول (DDL)
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3">
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-3 rounded-lg text-sm transition-all duration-200 border border-blue-200 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    age INTEGER CHECK(age >= 0 AND age <= 120),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">👥 جدول المستخدمين</span>
                                    <span class="text-xs bg-blue-200 px-2 py-1 rounded">DDL</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">إنشاء جدول مع قيود متقدمة</div>
                            </button>
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-3 rounded-lg text-sm transition-all duration-200 border border-blue-200 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    price DECIMAL(10,2) CHECK(price > 0),
    category TEXT DEFAULT 'عام',
    in_stock BOOLEAN DEFAULT 1
);">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">🛍️ جدول المنتجات</span>
                                    <span class="text-xs bg-blue-200 px-2 py-1 rounded">DDL</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">جدول منتجات مع أسعار وحالة</div>
                            </button>
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-3 rounded-lg text-sm transition-all duration-200 border border-blue-200 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER CHECK(quantity > 0),
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">📦 جدول الطلبات</span>
                                    <span class="text-xs bg-blue-200 px-2 py-1 rounded">DDL</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">جدول مع مفاتيح خارجية</div>
                            </button>
                            <button class="sample-query bg-blue-50 hover:bg-blue-100 p-3 rounded-lg text-sm transition-all duration-200 border border-blue-200 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_products_category ON products(category);">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">🔍 إنشاء فهارس</span>
                                    <span class="text-xs bg-blue-200 px-2 py-1 rounded">INDEX</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">فهارس لتحسين الأداء</div>
                            </button>
                        </div>
                    </div>

                        <!-- أمثلة DML -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-700 mb-3 flex items-center">
                                <span class="ml-2">📝</span>
                                إدراج البيانات (DML)
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3">
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-3 rounded-lg text-sm transition-all duration-200 border border-green-200 hover:border-green-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="INSERT INTO users (name, email, age) VALUES
('أحمد محمد', '<EMAIL>', 25),
('فاطمة علي', '<EMAIL>', 30),
('محمد حسن', '<EMAIL>', 28),
('سارة أحمد', '<EMAIL>', 22),
('علي محمود', '<EMAIL>', 35);">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">👥 إدراج مستخدمين</span>
                                    <span class="text-xs bg-green-200 px-2 py-1 rounded">INSERT</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">بيانات تجريبية للمستخدمين</div>
                            </button>
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-3 rounded-lg text-sm transition-all duration-200 border border-green-200 hover:border-green-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="INSERT INTO products (name, price, category) VALUES
('لابتوب Dell', 2500.00, 'إلكترونيات'),
('هاتف iPhone', 3000.00, 'إلكترونيات'),
('كتاب البرمجة', 50.00, 'كتب'),
('قميص قطني', 80.00, 'ملابس'),
('حقيبة جلدية', 150.00, 'إكسسوارات');">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">🛍️ إدراج منتجات</span>
                                    <span class="text-xs bg-green-200 px-2 py-1 rounded">INSERT</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">منتجات متنوعة مع أسعار</div>
                            </button>
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-3 rounded-lg text-sm transition-all duration-200 border border-green-200 hover:border-green-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="INSERT INTO orders (user_id, product_id, quantity) VALUES
(1, 1, 1),
(2, 2, 1),
(3, 3, 2),
(1, 4, 3),
(4, 5, 1);">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">📦 إدراج طلبات</span>
                                    <span class="text-xs bg-green-200 px-2 py-1 rounded">INSERT</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">ربط المستخدمين بالمنتجات</div>
                            </button>
                            <button class="sample-query bg-green-50 hover:bg-green-100 p-3 rounded-lg text-sm transition-all duration-200 border border-green-200 hover:border-green-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="UPDATE products
SET price = price * 0.9
WHERE category = 'ملابس';

UPDATE users
SET age = age + 1
WHERE id IN (1, 2, 3);">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">✏️ تحديث البيانات</span>
                                    <span class="text-xs bg-green-200 px-2 py-1 rounded">UPDATE</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">تحديث الأسعار والأعمار</div>
                            </button>
                            </div>
                        </div>

                        <!-- أمثلة الاستعلامات -->
                        <div>
                            <h4 class="text-md font-medium text-gray-700 mb-3 flex items-center">
                                <span class="ml-2">🔍</span>
                                الاستعلامات المتقدمة
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3">
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-3 rounded-lg text-sm transition-all duration-200 border border-purple-200 hover:border-purple-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="SELECT * FROM users ORDER BY age DESC;">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">👥 عرض المستخدمين</span>
                                    <span class="text-xs bg-purple-200 px-2 py-1 rounded">SELECT</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">ترتيب حسب العمر</div>
                            </button>
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-3 rounded-lg text-sm transition-all duration-200 border border-purple-200 hover:border-purple-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="SELECT
    u.name as اسم_المستخدم,
    p.name as اسم_المنتج,
    o.quantity as الكمية,
    (p.price * o.quantity) as المجموع
FROM orders o
JOIN users u ON o.user_id = u.id
JOIN products p ON o.product_id = p.id
ORDER BY المجموع DESC;">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">📊 تقرير الطلبات</span>
                                    <span class="text-xs bg-purple-200 px-2 py-1 rounded">JOIN</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">ربط ثلاث جداول</div>
                            </button>
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-3 rounded-lg text-sm transition-all duration-200 border border-purple-200 hover:border-purple-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="SELECT
    category as الفئة,
    COUNT(*) as عدد_المنتجات,
    AVG(price) as متوسط_السعر,
    MIN(price) as أقل_سعر,
    MAX(price) as أعلى_سعر
FROM products
GROUP BY category
ORDER BY عدد_المنتجات DESC;">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">📈 إحصائيات المنتجات</span>
                                    <span class="text-xs bg-purple-200 px-2 py-1 rounded">GROUP BY</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">تجميع وإحصائيات</div>
                            </button>
                            <button class="sample-query bg-purple-50 hover:bg-purple-100 p-3 rounded-lg text-sm transition-all duration-200 border border-purple-200 hover:border-purple-300 hover:shadow-md transform hover:-translate-y-1"
                                    data-query="SELECT
    u.name,
    u.age,
    COUNT(o.id) as عدد_الطلبات,
    COALESCE(SUM(p.price * o.quantity), 0) as إجمالي_المشتريات
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
LEFT JOIN products p ON o.product_id = p.id
GROUP BY u.id, u.name, u.age
ORDER BY إجمالي_المشتريات DESC;">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">🎯 تحليل العملاء</span>
                                    <span class="text-xs bg-purple-200 px-2 py-1 rounded">LEFT JOIN</span>
                                </div>
                                <div class="text-xs text-gray-600 mt-1 text-right">تحليل شامل للعملاء</div>
                            </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Panel -->
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">النتائج</h2>
                    <div id="results-panel" class="results-table">
                        <p class="text-gray-500 text-sm">قم بتشغيل أمر SQL لعرض النتائج هنا</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="text-gray-700">جاري تحميل محرك SQL...</span>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
