# محرر ومترجم SQL - SQL Editor & Compiler

محرر ومترجم SQL متكامل يعمل بالكامل في المتصفح، يتيح للمستخدمين كتابة وتنفيذ أوامر SQL (DDL و DML) بشكل فوري مع عرض النتائج والجداول بصريًا.

## المميزات الرئيسية

### 🚀 التقنيات المستخدمة
- **HTML5**: الهيكل الأساسي للتطبيق
- **Tailwind CSS**: تصميم واجهة مستخدم حديثة وسريعة الاستجابة
- **JavaScript ES6+**: منطق التطبيق والتفاعل
- **sql.js**: محرك SQLite مجمع إلى WebAssembly للعمل في المتصفح
- **Monaco Editor**: م<PERSON><PERSON><PERSON> الكود المتقدم من Visual Studio Code

### ✨ المميزات الوظيفية
- **محرر SQL متقدم**: مع تمييز الألوان والإكمال التلقائي
- **تنفيذ فوري**: تشغيل أوامر SQL مباشرة في المتصفح
- **عرض الجداول**: لوحة جانبية تعرض هيكل قاعدة البيانات
- **عرض النتائج**: جداول منسقة لعرض نتائج الاستعلامات
- **دعم كامل للقيود**: PRIMARY KEY, FOREIGN KEY, NOT NULL, UNIQUE, CHECK
- **أمثلة سريعة**: مجموعة من الأوامر الجاهزة للاختبار
- **رسائل الأخطاء**: عرض واضح لأخطاء SQL والقيود

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
# افتح ملف index.html في المتصفح مباشرة
# أو استخدم خادم محلي
python -m http.server 8000
# ثم افتح http://localhost:8000
```

### 2. أمثلة على الأوامر المدعومة

#### إنشاء الجداول (DDL)
```sql
-- إنشاء جدول المستخدمين
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    age INTEGER CHECK(age >= 0),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المنتجات
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    price DECIMAL(10,2) CHECK(price > 0),
    category_id INTEGER,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### إدراج البيانات (DML)
```sql
-- إدراج مستخدمين جدد
INSERT INTO users (name, email, age) VALUES 
('أحمد محمد', '<EMAIL>', 25),
('فاطمة علي', '<EMAIL>', 30),
('محمد حسن', '<EMAIL>', 28);

-- إدراج منتجات
INSERT INTO products (name, price) VALUES 
('لابتوب', 2500.00),
('هاتف ذكي', 800.00),
('تابلت', 400.00);
```

#### الاستعلامات (SELECT)
```sql
-- عرض جميع المستخدمين
SELECT * FROM users ORDER BY age DESC;

-- البحث المتقدم
SELECT name, age FROM users WHERE age > 25;

-- الانضمام بين الجداول
SELECT u.name, p.name as product_name, p.price 
FROM users u 
JOIN orders o ON u.id = o.user_id 
JOIN products p ON o.product_id = p.id;
```

#### تحديث وحذف البيانات
```sql
-- تحديث عمر مستخدم
UPDATE users SET age = 26 WHERE name = 'أحمد محمد';

-- حذف المنتجات الرخيصة
DELETE FROM products WHERE price < 100;
```

### 3. واجهة المستخدم

#### محرر SQL
- اكتب أوامر SQL في المحرر المتقدم
- استخدم Ctrl+Enter لتنفيذ الأمر
- استفد من تمييز الألوان والإكمال التلقائي

#### لوحة الجداول
- عرض جميع الجداول المنشأة
- تفاصيل كل جدول (الأعمدة، الأنواع، القيود)
- تحديث تلقائي عند إنشاء جداول جديدة

#### منطقة النتائج
- عرض نتائج SELECT في جداول منسقة
- رسائل نجاح للأوامر DDL/DML
- رسائل خطأ واضحة مع التفاصيل

## الأوامر المدعومة

### أوامر DDL (Data Definition Language)
- `CREATE TABLE` - إنشاء جداول جديدة
- `ALTER TABLE` - تعديل هيكل الجداول
- `DROP TABLE` - حذف الجداول
- `CREATE INDEX` - إنشاء فهارس

### أوامر DML (Data Manipulation Language)
- `SELECT` - استعلام البيانات
- `INSERT` - إدراج بيانات جديدة
- `UPDATE` - تحديث البيانات الموجودة
- `DELETE` - حذف البيانات

### القيود المدعومة
- `PRIMARY KEY` - المفتاح الأساسي
- `FOREIGN KEY` - المفتاح الخارجي
- `NOT NULL` - منع القيم الفارغة
- `UNIQUE` - ضمان التفرد
- `CHECK` - قيود التحقق المخصصة
- `DEFAULT` - القيم الافتراضية

## اختصارات لوحة المفاتيح
- `Ctrl + Enter`: تنفيذ الأمر الحالي
- `Ctrl + A`: تحديد كل النص
- `Ctrl + Z`: تراجع
- `Ctrl + Y`: إعادة

## ملاحظات تقنية
- يعمل التطبيق بالكامل في المتصفح دون الحاجة لخادم
- قاعدة البيانات مؤقتة في الذاكرة (تُفقد عند إعادة تحميل الصفحة)
- يدعم جميع ميزات SQLite الأساسية
- متوافق مع جميع المتصفحات الحديثة

## استكشاف الأخطاء
- تأكد من اتصال الإنترنت لتحميل المكتبات الخارجية
- تحقق من صحة صيغة SQL المكتوبة
- راجع رسائل الخطأ في منطقة النتائج للحصول على تفاصيل المشكلة
