class SimpleSQLEditor {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            this.showLoading(true);
            await this.initSQLJS();
            this.setupEventListeners();
            this.isInitialized = true;
            this.showLoading(false);
            this.showMessage('تم تحميل محرر SQL بنجاح! 🎉', 'success');
        } catch (error) {
            this.showLoading(false);
            this.showMessage('خطأ في تحميل المحرر: ' + error.message, 'error');
        }
    }

    async initSQLJS() {
        const sqlPromise = initSqlJs({
            locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
        });
        const SQL = await sqlPromise;
        this.db = new SQL.Database();
        console.log('✅ تم تهيئة محرك SQL بنجاح');
    }

    setupEventListeners() {
        // زر تنفيذ الأمر
        const executeBtn = document.getElementById('execute-btn');
        if (executeBtn) {
            executeBtn.addEventListener('click', () => this.executeSQL());
        }

        // زر المسح
        const clearBtn = document.getElementById('clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearEditor());
        }

        // الأمثلة السريعة
        document.querySelectorAll('.sample-btn, .sample-query').forEach(button => {
            button.addEventListener('click', (e) => {
                const query = e.target.getAttribute('data-query') || e.target.closest('[data-query]')?.getAttribute('data-query');
                if (query) {
                    this.loadQuery(query);
                }
            });
        });

        // اختصار لوحة المفاتيح
        const editor = document.getElementById('sql-editor');
        if (editor) {
            editor.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    this.executeSQL();
                }
            });
        }
    }

    loadQuery(query) {
        const editor = document.getElementById('sql-editor');
        if (editor) {
            editor.value = query;
            editor.focus();
            // تحريك المؤشر إلى نهاية النص
            editor.setSelectionRange(query.length, query.length);
        }
    }

    clearEditor() {
        const editor = document.getElementById('sql-editor');
        if (editor) {
            editor.value = '';
            editor.focus();
        }
    }

    executeSQL() {
        if (!this.isInitialized) {
            this.showMessage('⏳ المحرر لم يتم تحميله بعد، يرجى الانتظار...', 'warning');
            return;
        }

        const editor = document.getElementById('sql-editor');
        if (!editor) {
            this.showMessage('❌ خطأ: لم يتم العثور على المحرر', 'error');
            return;
        }

        const sqlQuery = editor.value.trim();
        if (!sqlQuery) {
            this.showMessage('⚠️ يرجى كتابة أمر SQL أولاً', 'warning');
            return;
        }

        try {
            const startTime = performance.now();
            const results = this.db.exec(sqlQuery);
            const endTime = performance.now();
            const executionTime = (endTime - startTime).toFixed(2);

            this.displayResults(results, executionTime);
            this.updateSchemaPanel();
            
        } catch (error) {
            this.showMessage('❌ خطأ في تنفيذ الأمر: ' + error.message, 'error');
        }
    }

    displayResults(results, executionTime) {
        const resultsPanel = document.getElementById('results-panel');
        if (!resultsPanel) return;
        
        if (results.length === 0) {
            // أمر تم تنفيذه بنجاح ولكن لا يوجد نتائج
            resultsPanel.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <span class="text-green-600 text-lg ml-2">✅</span>
                        <span class="text-green-800 font-medium">تم تنفيذ الأمر بنجاح</span>
                    </div>
                    <p class="text-green-700 text-sm mt-1">⏱️ وقت التنفيذ: ${executionTime} مللي ثانية</p>
                </div>
            `;
        } else {
            // عرض نتائج SELECT
            let html = `
                <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <span class="text-sm font-medium text-blue-800">📊 نتائج الاستعلام - وقت التنفيذ: ${executionTime} مللي ثانية</span>
                </div>
            `;
            
            results.forEach((result, index) => {
                if (result.columns && result.values) {
                    html += this.createTable(result.columns, result.values, index);
                }
            });
            
            resultsPanel.innerHTML = html;
        }
    }

    createTable(columns, values, index) {
        if (values.length === 0) {
            return `
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                    <p class="text-yellow-800">⚠️ لا توجد نتائج للاستعلام ${index + 1}</p>
                </div>
            `;
        }

        let tableHTML = `
            <div class="mb-6 bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="bg-gray-50 px-4 py-2 border-b">
                    <h3 class="font-medium text-gray-800">📋 نتائج الاستعلام ${index + 1} (${values.length} صف)</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
        `;
        
        // إضافة رؤوس الأعمدة
        columns.forEach(column => {
            tableHTML += `<th class="px-4 py-2 text-right font-medium text-gray-700 border-b">${this.escapeHtml(column)}</th>`;
        });
        
        tableHTML += `
                            </tr>
                        </thead>
                        <tbody>
        `;
        
        // إضافة الصفوف
        values.forEach((row, rowIndex) => {
            const bgClass = rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50';
            tableHTML += `<tr class="${bgClass}">`;
            row.forEach(cell => {
                let cellValue;
                if (cell === null) {
                    cellValue = '<span class="text-gray-400 italic">NULL</span>';
                } else if (typeof cell === 'number') {
                    cellValue = `<span class="font-mono text-blue-600">${cell.toLocaleString('ar-EG')}</span>`;
                } else {
                    cellValue = this.escapeHtml(String(cell));
                }
                tableHTML += `<td class="px-4 py-2 border-b border-gray-200">${cellValue}</td>`;
            });
            tableHTML += '</tr>';
        });
        
        tableHTML += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        return tableHTML;
    }

    updateSchemaPanel() {
        try {
            const tablesResult = this.db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
            const schemaPanel = document.getElementById('schema-panel');
            
            if (!schemaPanel) return;
            
            if (tablesResult.length === 0 || tablesResult[0].values.length === 0) {
                schemaPanel.innerHTML = '<p class="text-gray-500 text-sm text-center">لا توجد جداول</p>';
                return;
            }
            
            let html = '';
            const tables = tablesResult[0].values.map(row => row[0]);
            
            tables.forEach(tableName => {
                html += this.createTableSchema(tableName);
            });
            
            schemaPanel.innerHTML = html;
            
        } catch (error) {
            console.error('خطأ في تحديث عرض الجداول:', error);
        }
    }

    createTableSchema(tableName) {
        try {
            const columnsResult = this.db.exec(`PRAGMA table_info(${tableName})`);
            
            // الحصول على عدد الصفوف
            let rowCount = 0;
            try {
                const countResult = this.db.exec(`SELECT COUNT(*) as count FROM ${tableName}`);
                if (countResult.length > 0 && countResult[0].values.length > 0) {
                    rowCount = countResult[0].values[0][0];
                }
            } catch (e) {
                // تجاهل الأخطاء
            }
            
            let html = `
                <div class="mb-3 p-3 border border-gray-200 rounded-lg bg-white">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-medium text-blue-800">📋 ${tableName}</h4>
                        <span class="text-xs bg-blue-100 px-2 py-1 rounded">${rowCount} صف</span>
                    </div>
                    <div class="space-y-1">
            `;
            
            if (columnsResult.length > 0 && columnsResult[0].values.length > 0) {
                columnsResult[0].values.forEach(column => {
                    const [cid, name, type, notnull, defaultValue, pk] = column;
                    const constraints = [];
                    if (pk) constraints.push('🔑 PK');
                    if (notnull) constraints.push('⚠️ NOT NULL');
                    
                    html += `
                        <div class="text-xs p-2 bg-gray-50 rounded border">
                            <div class="font-mono text-blue-600 font-semibold">${name}</div>
                            <div class="text-gray-600">${type}</div>
                            ${constraints.length > 0 ? `<div class="text-orange-600 text-xs">${constraints.join(' • ')}</div>` : ''}
                        </div>
                    `;
                });
            }
            
            html += `
                    </div>
                    <div class="mt-2 flex gap-1">
                        <button class="text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded transition-colors" 
                                onclick="sqlEditor.loadQuery('SELECT * FROM ${tableName} LIMIT 10;')">
                            عرض البيانات
                        </button>
                        <button class="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors" 
                                onclick="sqlEditor.loadQuery('SELECT COUNT(*) as total_rows FROM ${tableName};')">
                            عدد الصفوف
                        </button>
                    </div>
                </div>
            `;
            
            return html;
            
        } catch (error) {
            return `
                <div class="mb-3 p-3 border border-red-200 rounded-lg bg-red-50">
                    <h4 class="font-medium text-red-800">❌ ${tableName}</h4>
                    <p class="text-xs text-red-600">خطأ في قراءة الهيكل</p>
                </div>
            `;
        }
    }

    showMessage(message, type = 'info') {
        const resultsPanel = document.getElementById('results-panel');
        if (!resultsPanel) return;
        
        const colors = {
            success: { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800' },
            error: { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-800' },
            warning: { bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-800' },
            info: { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800' }
        };
        
        const config = colors[type] || colors.info;
        
        resultsPanel.innerHTML = `
            <div class="${config.bg} ${config.border} rounded-lg p-4">
                <p class="${config.text} leading-relaxed">${message}</p>
            </div>
        `;
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// متغير عام للوصول إلى مثيل المحرر
let sqlEditor;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    sqlEditor = new SimpleSQLEditor();
});
