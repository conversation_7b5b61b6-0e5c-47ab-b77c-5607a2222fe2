-- ملف أمثلة SQL للاختبار
-- يمكن نسخ هذه الأوامر واختبارها في المحرر

-- 1. إنشاء قاعدة بيانات متجر إلكتروني كاملة

-- إنشاء جدول الفئات
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المستخدمين
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT,
    birth_date DATE,
    gender TEXT CHECK(gender IN ('ذكر', 'أنثى')),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المنتجات
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL CHECK(price > 0),
    cost_price DECIMAL(10,2) CHECK(cost_price >= 0),
    category_id INTEGER NOT NULL,
    stock_quantity INTEGER DEFAULT 0 CHECK(stock_quantity >= 0),
    min_stock_level INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT 1,
    weight DECIMAL(8,3),
    dimensions TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- إنشاء جدول العناوين
CREATE TABLE addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type TEXT CHECK(type IN ('منزل', 'عمل', 'أخرى')) DEFAULT 'منزل',
    street_address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    postal_code TEXT,
    country TEXT DEFAULT 'السعودية',
    is_default BOOLEAN DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- إنشاء جدول الطلبات
CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    address_id INTEGER NOT NULL,
    status TEXT CHECK(status IN ('معلق', 'مؤكد', 'قيد التحضير', 'مشحون', 'مسلم', 'ملغي')) DEFAULT 'معلق',
    total_amount DECIMAL(10,2) NOT NULL CHECK(total_amount >= 0),
    shipping_cost DECIMAL(8,2) DEFAULT 0,
    tax_amount DECIMAL(8,2) DEFAULT 0,
    discount_amount DECIMAL(8,2) DEFAULT 0,
    payment_method TEXT CHECK(payment_method IN ('نقدي', 'بطاقة ائتمان', 'تحويل بنكي', 'محفظة إلكترونية')),
    payment_status TEXT CHECK(payment_status IN ('معلق', 'مدفوع', 'فشل', 'مسترد')) DEFAULT 'معلق',
    notes TEXT,
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    shipped_date DATETIME,
    delivered_date DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (address_id) REFERENCES addresses(id)
);

-- إنشاء جدول تفاصيل الطلبات
CREATE TABLE order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL CHECK(quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK(unit_price > 0),
    total_price DECIMAL(10,2) NOT NULL CHECK(total_price > 0),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- إنشاء جدول المراجعات
CREATE TABLE reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    rating INTEGER NOT NULL CHECK(rating BETWEEN 1 AND 5),
    title TEXT,
    comment TEXT,
    is_verified_purchase BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE(user_id, product_id)
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_order_items_product ON order_items(product_id);
CREATE INDEX idx_reviews_product ON reviews(product_id);
CREATE INDEX idx_addresses_user ON addresses(user_id);

-- 2. إدراج بيانات تجريبية

-- إدراج الفئات
INSERT INTO categories (name, description) VALUES 
('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية'),
('ملابس', 'ملابس رجالية ونسائية وأطفال'),
('كتب', 'كتب ومراجع علمية وأدبية'),
('رياضة', 'معدات ومستلزمات رياضية'),
('منزل ومطبخ', 'أدوات منزلية ومطبخية'),
('جمال وعناية', 'منتجات التجميل والعناية الشخصية');

-- إدراج المستخدمين
INSERT INTO users (username, email, password_hash, full_name, phone, birth_date, gender) VALUES 
('ahmed123', '<EMAIL>', 'hash123', 'أحمد محمد علي', '0501234567', '1990-05-15', 'ذكر'),
('fatima_ali', '<EMAIL>', 'hash456', 'فاطمة علي حسن', '0507654321', '1992-08-22', 'أنثى'),
('mohammed_h', '<EMAIL>', 'hash789', 'محمد حسن أحمد', '0509876543', '1988-12-10', 'ذكر'),
('sara_omar', '<EMAIL>', 'hash101', 'سارة عمر محمد', '0502468135', '1995-03-18', 'أنثى'),
('ali_salem', '<EMAIL>', 'hash202', 'علي سالم عبدالله', '0508642097', '1987-07-25', 'ذكر');

-- إدراج المنتجات
INSERT INTO products (name, description, price, cost_price, category_id, stock_quantity) VALUES 
('لابتوب Dell XPS 13', 'لابتوب عالي الأداء مع معالج Intel Core i7', 4500.00, 3800.00, 1, 15),
('هاتف iPhone 14', 'هاتف ذكي من Apple بكاميرا متطورة', 3200.00, 2800.00, 1, 25),
('سماعات Sony WH-1000XM4', 'سماعات لاسلكية بتقنية إلغاء الضوضاء', 850.00, 650.00, 1, 40),
('قميص قطني أزرق', 'قميص رجالي قطني 100% مقاس L', 120.00, 80.00, 2, 100),
('فستان صيفي', 'فستان نسائي أنيق للمناسبات', 250.00, 180.00, 2, 50),
('كتاب البرمجة بـ Python', 'دليل شامل لتعلم البرمجة', 85.00, 60.00, 3, 200),
('حذاء رياضي Nike', 'حذاء رياضي مريح للجري', 380.00, 280.00, 4, 75),
('مقلاة تيفال', 'مقلاة غير لاصقة مقاس 28 سم', 150.00, 100.00, 5, 60),
('كريم مرطب للوجه', 'كريم طبيعي للعناية بالبشرة', 95.00, 65.00, 6, 120);

-- إدراج العناوين
INSERT INTO addresses (user_id, type, street_address, city, state, postal_code, is_default) VALUES 
(1, 'منزل', 'شارع الملك فهد، حي النخيل', 'الرياض', 'الرياض', '12345', 1),
(2, 'منزل', 'طريق الأمير محمد بن عبدالعزيز', 'جدة', 'مكة المكرمة', '23456', 1),
(3, 'عمل', 'شارع التحلية، برج الفيصلية', 'الرياض', 'الرياض', '12346', 1),
(4, 'منزل', 'كورنيش الدمام، حي الشاطئ', 'الدمام', 'الشرقية', '34567', 1),
(5, 'منزل', 'شارع الأمير سلطان، حي العليا', 'الرياض', 'الرياض', '12347', 1);

-- إدراج الطلبات
INSERT INTO orders (user_id, address_id, status, total_amount, shipping_cost, tax_amount, payment_method, payment_status) VALUES 
(1, 1, 'مسلم', 4650.00, 50.00, 100.00, 'بطاقة ائتمان', 'مدفوع'),
(2, 2, 'مشحون', 1100.00, 30.00, 70.00, 'تحويل بنكي', 'مدفوع'),
(3, 3, 'قيد التحضير', 465.00, 25.00, 40.00, 'محفظة إلكترونية', 'مدفوع'),
(4, 4, 'مؤكد', 335.00, 20.00, 15.00, 'نقدي', 'معلق'),
(1, 1, 'معلق', 235.00, 15.00, 20.00, 'بطاقة ائتمان', 'معلق');

-- إدراج تفاصيل الطلبات
INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES 
(1, 1, 1, 4500.00, 4500.00),
(2, 3, 1, 850.00, 850.00),
(2, 4, 2, 120.00, 240.00),
(3, 7, 1, 380.00, 380.00),
(3, 9, 1, 95.00, 95.00),
(4, 5, 1, 250.00, 250.00),
(4, 6, 1, 85.00, 85.00),
(5, 8, 1, 150.00, 150.00),
(5, 9, 1, 95.00, 95.00);

-- إدراج المراجعات
INSERT INTO reviews (user_id, product_id, rating, title, comment, is_verified_purchase) VALUES 
(1, 1, 5, 'ممتاز!', 'لابتوب رائع وسريع جداً، أنصح به بشدة', 1),
(2, 3, 4, 'جودة صوت عالية', 'سماعات ممتازة لكن السعر مرتفع قليلاً', 1),
(2, 4, 5, 'قميص مريح', 'قماش ممتاز ومقاس مناسب', 1),
(3, 7, 4, 'حذاء مريح', 'مناسب للجري لكن يحتاج وقت للتعود عليه', 1),
(4, 5, 5, 'فستان جميل', 'تصميم أنيق وخامة ممتازة', 1);

-- 3. استعلامات تحليلية متقدمة

-- تقرير المبيعات الشهرية
SELECT 
    strftime('%Y-%m', order_date) as الشهر,
    COUNT(*) as عدد_الطلبات,
    SUM(total_amount) as إجمالي_المبيعات,
    AVG(total_amount) as متوسط_قيمة_الطلب,
    MIN(total_amount) as أقل_طلب,
    MAX(total_amount) as أعلى_طلب
FROM orders 
WHERE payment_status = 'مدفوع'
GROUP BY strftime('%Y-%m', order_date)
ORDER BY الشهر DESC;

-- أفضل المنتجات مبيعاً
SELECT 
    p.name as اسم_المنتج,
    c.name as الفئة,
    SUM(oi.quantity) as الكمية_المباعة,
    SUM(oi.total_price) as إجمالي_المبيعات,
    AVG(r.rating) as متوسط_التقييم,
    COUNT(r.id) as عدد_المراجعات
FROM products p
JOIN categories c ON p.category_id = c.id
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN reviews r ON p.id = r.product_id
GROUP BY p.id, p.name, c.name
HAVING SUM(oi.quantity) > 0
ORDER BY الكمية_المباعة DESC;

-- تحليل العملاء
SELECT 
    u.full_name as اسم_العميل,
    u.email as البريد_الإلكتروني,
    COUNT(o.id) as عدد_الطلبات,
    SUM(o.total_amount) as إجمالي_المشتريات,
    AVG(o.total_amount) as متوسط_قيمة_الطلب,
    MAX(o.order_date) as آخر_طلب,
    CASE 
        WHEN SUM(o.total_amount) > 5000 THEN 'VIP'
        WHEN SUM(o.total_amount) > 2000 THEN 'ذهبي'
        WHEN SUM(o.total_amount) > 500 THEN 'فضي'
        ELSE 'عادي'
    END as فئة_العميل
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, u.full_name, u.email
ORDER BY إجمالي_المشتريات DESC;

-- تقرير المخزون
SELECT 
    p.name as اسم_المنتج,
    c.name as الفئة,
    p.stock_quantity as الكمية_الحالية,
    p.min_stock_level as الحد_الأدنى,
    CASE 
        WHEN p.stock_quantity <= p.min_stock_level THEN 'نفاد وشيك'
        WHEN p.stock_quantity <= p.min_stock_level * 2 THEN 'منخفض'
        ELSE 'طبيعي'
    END as حالة_المخزون,
    p.price as السعر,
    (p.price - p.cost_price) as هامش_الربح,
    ROUND(((p.price - p.cost_price) / p.price) * 100, 2) as نسبة_الربح
FROM products p
JOIN categories c ON p.category_id = c.id
WHERE p.is_active = 1
ORDER BY حالة_المخزون, p.stock_quantity;

-- 4. أوامر صيانة وإدارة

-- تحديث أسعار فئة معينة
UPDATE products 
SET price = price * 1.1, updated_at = CURRENT_TIMESTAMP 
WHERE category_id = 1;

-- حذف الطلبات الملغاة القديمة
DELETE FROM orders 
WHERE status = 'ملغي' 
AND order_date < date('now', '-30 days');

-- إنشاء نسخة احتياطية من بيانات المستخدمين النشطين
CREATE TABLE users_backup AS 
SELECT * FROM users WHERE is_active = 1;

-- تحديث حالة المنتجات منخفضة المخزون
UPDATE products 
SET is_active = 0 
WHERE stock_quantity = 0;
