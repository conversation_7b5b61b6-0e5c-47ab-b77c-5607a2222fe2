class SQLEditor {
    constructor() {
        this.db = null;
        this.editor = null;
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            this.showLoading(true);
            await this.initSQLJS();
            await this.initMonacoEditor();
            this.setupEventListeners();
            this.isInitialized = true;
            this.showLoading(false);
            this.showMessage('تم تحميل محرر SQL بنجاح!', 'success');
        } catch (error) {
            this.showLoading(false);
            this.showMessage('خطأ في تحميل المحرر: ' + error.message, 'error');
        }
    }

    async initSQLJS() {
        const sqlPromise = initSqlJs({
            locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
        });
        const SQL = await sqlPromise;
        this.db = new SQL.Database();
        console.log('تم تهيئة محرك SQL بنجاح');
    }

    async initMonacoEditor() {
        return new Promise((resolve) => {
            require.config({ 
                paths: { 
                    'vs': 'https://unpkg.com/monaco-editor@0.44.0/min/vs' 
                } 
            });
            
            require(['vs/editor/editor.main'], () => {
                this.editor = monaco.editor.create(document.getElementById('sql-editor'), {
                    value: '-- اكتب أوامر SQL هنا\nSELECT "مرحباً بك في محرر SQL!" as message;',
                    language: 'sql',
                    theme: 'vs',
                    automaticLayout: true,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    lineNumbers: 'on',
                    roundedSelection: false,
                    scrollbar: {
                        vertical: 'visible',
                        horizontal: 'visible'
                    }
                });
                
                // إضافة اختصار لوحة المفاتيح لتنفيذ الأمر
                this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
                    this.executeSQL();
                });
                
                resolve();
            });
        });
    }

    setupEventListeners() {
        // زر تنفيذ الأمر
        document.getElementById('execute-btn').addEventListener('click', () => {
            this.executeSQL();
        });

        // زر المسح
        document.getElementById('clear-btn').addEventListener('click', () => {
            this.editor.setValue('');
            this.editor.focus();
        });

        // الأمثلة السريعة
        document.querySelectorAll('.sample-query').forEach(button => {
            button.addEventListener('click', (e) => {
                const query = e.target.getAttribute('data-query');
                this.editor.setValue(query);
                this.editor.focus();
            });
        });
    }

    executeSQL() {
        if (!this.isInitialized) {
            this.showMessage('المحرر لم يتم تحميله بعد، يرجى الانتظار...', 'warning');
            return;
        }

        const sqlQuery = this.editor.getValue().trim();
        if (!sqlQuery) {
            this.showMessage('يرجى كتابة أمر SQL أولاً', 'warning');
            return;
        }

        try {
            const startTime = performance.now();
            
            // تنفيذ الأمر
            const results = this.db.exec(sqlQuery);
            
            const endTime = performance.now();
            const executionTime = (endTime - startTime).toFixed(2);

            // عرض النتائج
            this.displayResults(results, executionTime);
            
            // تحديث عرض الجداول
            this.updateSchemaPanel();
            
        } catch (error) {
            this.showMessage('خطأ في تنفيذ الأمر: ' + error.message, 'error');
        }
    }

    displayResults(results, executionTime) {
        const resultsPanel = document.getElementById('results-panel');

        if (results.length === 0) {
            // أمر تم تنفيذه بنجاح ولكن لا يوجد نتائج (مثل INSERT, UPDATE, DELETE, CREATE)
            resultsPanel.innerHTML = `
                <div class="success-message status-message">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 ml-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-green-800 font-medium">✅ تم تنفيذ الأمر بنجاح</span>
                    </div>
                    <p class="text-green-700 text-sm mt-2">⏱️ وقت التنفيذ: ${executionTime} مللي ثانية</p>
                </div>
            `;
        } else {
            // عرض نتائج SELECT
            let html = `
                <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-blue-800">📊 نتائج الاستعلام</span>
                        <span class="text-xs text-blue-600">⏱️ ${executionTime} مللي ثانية</span>
                    </div>
                </div>
            `;

            results.forEach((result, index) => {
                if (result.columns && result.values) {
                    html += this.createTable(result.columns, result.values, index);
                }
            });

            resultsPanel.innerHTML = html;
        }
    }

    createTable(columns, values, index) {
        if (values.length === 0) {
            return `
                <div class="warning-message status-message mb-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 ml-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-yellow-800 font-medium">⚠️ لا توجد نتائج للاستعلام ${index + 1}</span>
                    </div>
                </div>
            `;
        }

        let tableHTML = `
            <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="font-semibold text-gray-800 flex items-center">
                            <span class="table-icon">📋</span>
                            نتائج الاستعلام ${index + 1}
                        </h3>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-600 bg-white px-2 py-1 rounded">
                                📊 ${values.length} ${values.length === 1 ? 'صف' : 'صف'}
                            </span>
                            <span class="text-sm text-gray-600 bg-white px-2 py-1 rounded">
                                📝 ${columns.length} ${columns.length === 1 ? 'عمود' : 'عمود'}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="data-table min-w-full">
                        <thead>
                            <tr>
        `;

        // إضافة رؤوس الأعمدة
        columns.forEach(column => {
            tableHTML += `<th class="text-right">${this.escapeHtml(column)}</th>`;
        });

        tableHTML += `
                            </tr>
                        </thead>
                        <tbody>
        `;

        // إضافة الصفوف
        values.forEach((row, rowIndex) => {
            tableHTML += `<tr>`;
            row.forEach((cell, cellIndex) => {
                let cellValue;
                if (cell === null) {
                    cellValue = '<span class="null-value">NULL</span>';
                } else if (typeof cell === 'number') {
                    cellValue = `<span class="font-mono text-blue-600">${cell.toLocaleString('ar-EG')}</span>`;
                } else if (typeof cell === 'string' && cell.match(/^\d{4}-\d{2}-\d{2}/)) {
                    // تاريخ
                    cellValue = `<span class="text-purple-600">📅 ${this.escapeHtml(cell)}</span>`;
                } else if (typeof cell === 'string' && cell.includes('@')) {
                    // بريد إلكتروني
                    cellValue = `<span class="text-green-600">📧 ${this.escapeHtml(cell)}</span>`;
                } else {
                    cellValue = this.escapeHtml(String(cell));
                }

                tableHTML += `<td title="${this.escapeHtml(String(cell))}">${cellValue}</td>`;
            });
            tableHTML += '</tr>';
        });

        tableHTML += `
                        </tbody>
                    </table>
                </div>
                <div class="bg-gray-50 px-4 py-2 text-xs text-gray-500 border-t border-gray-200">
                    💡 نصيحة: يمكنك النقر على رؤوس الأعمدة للترتيب (قريباً)
                </div>
            </div>
        `;

        return tableHTML;
    }

    updateSchemaPanel() {
        try {
            // الحصول على قائمة الجداول
            const tablesResult = this.db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
            const schemaPanel = document.getElementById('schema-panel');
            
            if (tablesResult.length === 0 || tablesResult[0].values.length === 0) {
                schemaPanel.innerHTML = '<p class="text-gray-500 text-sm">لا توجد جداول بعد</p>';
                return;
            }
            
            let html = '';
            const tables = tablesResult[0].values.map(row => row[0]);
            
            tables.forEach(tableName => {
                html += this.createTableSchema(tableName);
            });
            
            schemaPanel.innerHTML = html;
            
        } catch (error) {
            console.error('خطأ في تحديث عرض الجداول:', error);
        }
    }

    createTableSchema(tableName) {
        try {
            // الحصول على معلومات الأعمدة
            const columnsResult = this.db.exec(`PRAGMA table_info(${tableName})`);

            // الحصول على عدد الصفوف
            let rowCount = 0;
            try {
                const countResult = this.db.exec(`SELECT COUNT(*) as count FROM ${tableName}`);
                if (countResult.length > 0 && countResult[0].values.length > 0) {
                    rowCount = countResult[0].values[0][0];
                }
            } catch (e) {
                // تجاهل الأخطاء في حساب الصفوف
            }

            let html = `
                <div class="mb-4 border border-gray-200 rounded-lg">
                    <div class="bg-blue-50 px-3 py-2 border-b border-gray-200">
                        <h4 class="font-medium text-blue-800 cursor-pointer flex justify-between items-center" onclick="this.nextElementSibling.classList.toggle('hidden')">
                            <span>📋 ${tableName}</span>
                            <span class="text-xs bg-blue-100 px-2 py-1 rounded">${rowCount} صف</span>
                        </h4>
                    </div>
                    <div class="p-3">
                        <div class="mb-2">
                            <button class="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded mr-1 transition-colors"
                                    onclick="sqlEditor.insertQuery('SELECT * FROM ${tableName} LIMIT 10;')">
                                عرض البيانات
                            </button>
                            <button class="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded mr-1 transition-colors"
                                    onclick="sqlEditor.insertQuery('DESCRIBE ${tableName};')">
                                وصف الجدول
                            </button>
                        </div>
            `;

            if (columnsResult.length > 0 && columnsResult[0].values.length > 0) {
                html += '<div class="space-y-1">';
                columnsResult[0].values.forEach(column => {
                    const [cid, name, type, notnull, defaultValue, pk] = column;
                    const constraints = [];
                    if (pk) constraints.push('🔑 PK');
                    if (notnull) constraints.push('⚠️ NOT NULL');
                    if (defaultValue !== null) constraints.push(`📝 DEFAULT: ${defaultValue}`);

                    html += `
                        <div class="text-xs p-2 bg-gray-50 rounded border">
                            <div class="font-mono text-blue-600 font-semibold">${name}</div>
                            <div class="text-gray-600 mt-1">${type}</div>
                            ${constraints.length > 0 ? `<div class="text-orange-600 mt-1 text-xs">${constraints.join(' • ')}</div>` : ''}
                        </div>
                    `;
                });
                html += '</div>';
            }

            // إضافة معلومات الفهارس إن وجدت
            try {
                const indexResult = this.db.exec(`PRAGMA index_list(${tableName})`);
                if (indexResult.length > 0 && indexResult[0].values.length > 0) {
                    html += '<div class="mt-3 pt-2 border-t border-gray-200">';
                    html += '<div class="text-xs font-medium text-gray-700 mb-1">الفهارس:</div>';
                    indexResult[0].values.forEach(index => {
                        const [seq, name, unique, origin, partial] = index;
                        html += `<div class="text-xs text-purple-600">🔍 ${name} ${unique ? '(فريد)' : ''}</div>`;
                    });
                    html += '</div>';
                }
            } catch (e) {
                // تجاهل أخطاء الفهارس
            }

            html += `
                    </div>
                </div>
            `;

            return html;

        } catch (error) {
            return `
                <div class="mb-4 border border-red-200 rounded-lg">
                    <div class="bg-red-50 px-3 py-2">
                        <h4 class="font-medium text-red-800">❌ ${tableName}</h4>
                        <p class="text-xs text-red-600">خطأ في قراءة الهيكل: ${error.message}</p>
                    </div>
                </div>
            `;
        }
    }

    insertQuery(query) {
        this.editor.setValue(query);
        this.editor.focus();
    }

    showMessage(message, type = 'info') {
        const resultsPanel = document.getElementById('results-panel');

        const messageConfig = {
            success: {
                class: 'success-message',
                icon: '✅',
                title: 'نجح العملية'
            },
            error: {
                class: 'error-message',
                icon: '❌',
                title: 'خطأ'
            },
            warning: {
                class: 'warning-message',
                icon: '⚠️',
                title: 'تحذير'
            },
            info: {
                class: 'bg-blue-50 border border-blue-200 rounded-lg p-4',
                icon: 'ℹ️',
                title: 'معلومات'
            }
        };

        const config = messageConfig[type] || messageConfig.info;

        resultsPanel.innerHTML = `
            <div class="${config.class} status-message">
                <div class="flex items-start">
                    <span class="text-lg ml-3 mt-1">${config.icon}</span>
                    <div class="flex-1">
                        <h4 class="font-medium mb-1">${config.title}</h4>
                        <p class="text-sm leading-relaxed">${message}</p>
                    </div>
                </div>
            </div>
        `;
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// متغير عام للوصول إلى مثيل المحرر
let sqlEditor;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    sqlEditor = new SQLEditor();
});
