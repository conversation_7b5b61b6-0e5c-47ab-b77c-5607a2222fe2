/* تحسينات إضافية للتصميم */

/* تحسين محرر Monaco */
.monaco-editor-container {
    height: 300px;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: border-color 0.3s ease;
}

.monaco-editor-container:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* تحسين جداول النتائج */
.results-table {
    max-height: 500px;
    overflow: auto;
    border-radius: 0.5rem;
}

.results-table::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.results-table::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.results-table::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.results-table::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* تحسين لوحة الجداول */
.schema-panel {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 4px;
}

.schema-panel::-webkit-scrollbar {
    width: 6px;
}

.schema-panel::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
}

.schema-panel::-webkit-scrollbar-thumb {
    background: #e2e8f0;
    border-radius: 3px;
}

.schema-panel::-webkit-scrollbar-thumb:hover {
    background: #cbd5e1;
}

/* تحسين الأزرار */
.sample-query {
    transition: all 0.2s ease;
    text-align: right;
    direction: rtl;
}

.sample-query:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sample-query:active {
    transform: translateY(0);
}

/* تحسين رسائل الحالة */
.status-message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين الجداول */
.data-table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
    padding: 12px 16px;
    border-bottom: 2px solid #e2e8f0;
}

.data-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover td {
    background-color: #f8fafc;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* تحسين عرض NULL */
.null-value {
    color: #94a3b8;
    font-style: italic;
    font-size: 0.875rem;
}

/* تحسين أيقونات الجداول */
.table-icon {
    display: inline-block;
    margin-left: 8px;
    font-size: 1.1em;
}

/* تحسين أزرار الإجراءات */
.action-button {
    transition: all 0.2s ease;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid transparent;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين عرض القيود */
.constraint-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.65rem;
    font-weight: 500;
    margin: 1px;
}

.constraint-pk {
    background-color: #fef3c7;
    color: #92400e;
}

.constraint-notnull {
    background-color: #fee2e2;
    color: #991b1b;
}

.constraint-default {
    background-color: #e0e7ff;
    color: #3730a3;
}

/* تحسين شاشة التحميل */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .monaco-editor-container {
        height: 250px;
    }
    
    .results-table {
        max-height: 300px;
    }
    
    .schema-panel {
        max-height: 400px;
    }
    
    .data-table {
        font-size: 0.875rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 12px;
    }
}

/* تحسين طباعة الصفحة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .monaco-editor-container {
        border: 1px solid #000;
        height: auto;
        min-height: 200px;
    }
    
    .results-table {
        max-height: none;
        overflow: visible;
    }
    
    .data-table {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .data-table th,
    .data-table td {
        border: 1px solid #000;
    }
}

/* تحسين إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز للوحة المفاتيح */
button:focus,
.sample-query:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* تحسين عرض الأخطاء */
.error-message {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border-left: 4px solid #ef4444;
    padding: 16px;
    border-radius: 0.5rem;
    margin: 8px 0;
}

.success-message {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-left: 4px solid #22c55e;
    padding: 16px;
    border-radius: 0.5rem;
    margin: 8px 0;
}

.warning-message {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-left: 4px solid #f59e0b;
    padding: 16px;
    border-radius: 0.5rem;
    margin: 8px 0;
}
